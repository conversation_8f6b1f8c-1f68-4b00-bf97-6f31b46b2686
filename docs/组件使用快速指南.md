# Shadcn Vue 组件使用快速指南

## 🚀 快速开始

### 1. 启动项目
```bash
# 安装依赖（如果还没有安装）
pnpm install

# 启动开发服务器
pnpm dev
```

### 2. 访问测试页面
1. 打开浏览器访问 `http://localhost:5173`
2. 以管理员身份登录（用户名：admin）
3. 在侧边栏点击"组件测试"
4. 测试各个组件功能

## 📦 已配置的组件

### Dialog 对话框
```vue
<template>
  <Dialog v-model:open="isOpen">
    <DialogTrigger as-child>
      <Button>打开对话框</Button>
    </DialogTrigger>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>对话框标题</DialogTitle>
        <DialogDescription>对话框描述</DialogDescription>
      </DialogHeader>
      <div class="py-4">
        <!-- 对话框内容 -->
      </div>
      <DialogFooter>
        <Button @click="isOpen = false">关闭</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup>
import { ref } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

const isOpen = ref(false)
</script>
```

### Drawer 抽屉
```vue
<template>
  <Drawer v-model:open="isOpen">
    <DrawerTrigger as-child>
      <Button>打开抽屉</Button>
    </DrawerTrigger>
    <DrawerContent>
      <div class="mx-auto w-full max-w-sm">
        <DrawerHeader>
          <DrawerTitle>抽屉标题</DrawerTitle>
          <DrawerDescription>抽屉描述</DrawerDescription>
        </DrawerHeader>
        <div class="p-4">
          <!-- 抽屉内容 -->
        </div>
        <DrawerFooter>
          <Button @click="isOpen = false">关闭</Button>
        </DrawerFooter>
      </div>
    </DrawerContent>
  </Drawer>
</template>

<script setup>
import { ref } from 'vue'
import { Drawer, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { Button } from '@/components/ui/button'

const isOpen = ref(false)
</script>
```

### Sonner Toast 通知
```vue
<template>
  <div>
    <Button @click="showSuccess">成功通知</Button>
    <Button @click="showError">错误通知</Button>
    <Button @click="showWithAction">带操作通知</Button>
  </div>
</template>

<script setup>
import { toast } from 'vue-sonner'
import { Button } from '@/components/ui/button'

const showSuccess = () => {
  toast.success('操作成功！', {
    description: '您的更改已保存'
  })
}

const showError = () => {
  toast.error('操作失败！', {
    description: '请检查网络连接后重试'
  })
}

const showWithAction = () => {
  toast('新消息', {
    description: '您有一条新通知',
    action: {
      label: '查看',
      onClick: () => console.log('查看消息')
    }
  })
}
</script>
```

### Tooltip 工具提示
```vue
<template>
  <Tooltip>
    <TooltipTrigger as-child>
      <Button variant="outline">悬停显示提示</Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>这是一个有用的提示信息</p>
    </TooltipContent>
  </Tooltip>
</template>

<script setup>
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
</script>
```

## 🛠️ Composables 工具

### 通知管理
```typescript
import { useNotifications, useBusinessNotifications } from '@/composables/useNotifications'

// 基础通知
const notifications = useNotifications()
notifications.success('成功消息')
notifications.error('错误消息')
notifications.warning('警告消息')
notifications.info('信息消息')

// 业务通知
const businessNotifications = useBusinessNotifications()
businessNotifications.saveSuccess('项目')
businessNotifications.saveError()
businessNotifications.deleteSuccess('文件')
businessNotifications.networkError()
```

### 对话框管理
```typescript
import { useDialog, useConfirmDialog, useBusinessDialogs } from '@/composables/useDialog'

// 基础对话框
const dialog = useDialog()
dialog.open()
dialog.close()

// 确认对话框
const confirmDialog = useConfirmDialog()
const confirmed = await confirmDialog.confirm({
  title: '确认操作',
  description: '确定要继续吗？',
  confirmText: '确认',
  cancelText: '取消'
})

// 业务对话框
const businessDialogs = useBusinessDialogs()
const shouldDelete = await businessDialogs.confirmDelete('项目')
const shouldSave = await businessDialogs.confirmSave(true)
```

## 🎨 样式和主题

### CSS 变量
项目使用 CSS 变量进行主题管理，主要变量包括：
- `--background` - 背景色
- `--foreground` - 前景色
- `--border` - 边框色
- `--popover` - 弹出层背景色
- `--popover-foreground` - 弹出层前景色

### 主题切换
组件自动支持亮色/暗色主题切换，通过 `theme="system"` 配置。

## 📱 响应式设计

### 移动端适配
- Dialog 在移动端会自动调整大小
- Drawer 特别适合移动端侧边栏场景
- Toast 通知位置会根据屏幕大小调整

### 断点配置
使用 Tailwind CSS 断点：
- `sm:` - 640px+
- `md:` - 768px+
- `lg:` - 1024px+
- `xl:` - 1280px+

## 🔧 高级用法

### 表单集成
```vue
<template>
  <Dialog v-model:open="formDialog.isOpen.value">
    <DialogTrigger as-child>
      <Button>编辑用户</Button>
    </DialogTrigger>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>编辑用户信息</DialogTitle>
      </DialogHeader>
      <form @submit.prevent="formDialog.handleSubmit">
        <div class="space-y-4">
          <div>
            <Label for="name">姓名</Label>
            <Input 
              id="name" 
              v-model="formDialog.formData.value.name" 
              :class="{ 'border-red-500': formDialog.errors.value.name }"
            />
            <p v-if="formDialog.errors.value.name" class="text-red-500 text-sm">
              {{ formDialog.errors.value.name }}
            </p>
          </div>
        </div>
        <DialogFooter class="mt-6">
          <Button type="button" variant="outline" @click="formDialog.close">
            取消
          </Button>
          <Button type="submit" :disabled="formDialog.isLoading.value">
            {{ formDialog.isLoading.value ? '保存中...' : '保存' }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup>
import { useFormDialog } from '@/composables/useDialog'
import { useBusinessNotifications } from '@/composables/useNotifications'

const notifications = useBusinessNotifications()

const formDialog = useFormDialog(
  { name: '', email: '' }, // 初始数据
  async (data) => {
    // 提交处理
    await saveUser(data)
    notifications.saveSuccess('用户')
  }
)
</script>
```

### Promise 通知
```typescript
const saveData = async () => {
  const promise = fetch('/api/save', {
    method: 'POST',
    body: JSON.stringify(data)
  })

  toast.promise(promise, {
    loading: '正在保存...',
    success: '保存成功！',
    error: '保存失败，请重试'
  })
}
```

## 🐛 常见问题

### 1. Toast 不显示
确保在 `App.vue` 中正确配置了 `<Toaster />` 组件。

### 2. Tooltip 不工作
确保整个应用被 `<TooltipProvider>` 包装。

### 3. Dialog 层级问题
检查 CSS 中的 z-index 设置，确保没有其他元素覆盖。

### 4. 样式不生效
确保导入了 `vue-sonner/style.css` 样式文件。

## 📚 更多资源

- [Shadcn Vue 官方文档](https://www.shadcn-vue.com/)
- [Vue Sonner 文档](https://vue-sonner.vercel.app/)
- [Reka UI 文档](https://reka-ui.com/)
- [Tailwind CSS 文档](https://tailwindcss.com/)

## 🎯 下一步

1. 在业务组件中使用这些基础组件
2. 根据需要扩展 Composables 功能
3. 添加更多 shadcn-vue 组件
4. 自定义主题和样式
5. 添加单元测试
