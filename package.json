{"name": "glass-erp-prototype", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.6.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "echarts": "^6.0.0", "lucide-vue-next": "^0.536.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "reka-ui": "^2.4.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.18", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2", "zod": "^4.0.15"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "shadcn-vue": "^2.2.0", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}