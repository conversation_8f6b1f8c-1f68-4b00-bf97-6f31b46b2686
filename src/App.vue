<script setup lang="ts">
import { onMounted } from 'vue'
import AppLayout from './components/layout/AppLayout.vue'
import { TooltipProvider } from '@/components/ui/tooltip'
import { Sonner } from '@/components/ui/sonner'

// 初始化主题
onMounted(() => {
  // 检查系统主题偏好或本地存储的主题设置
  const savedTheme = localStorage.getItem('theme')
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

  if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
})
</script>

<template>
  <!-- 全局 Tooltip Provider - 为整个应用提供 tooltip context -->
  <TooltipProvider :delay-duration="300" :skip-delay-duration="100">
    <!-- 主应用布局 -->
    <AppLayout />

    <!-- 全局 Toast 通知组件 - 放在应用最外层以确保正确的 z-index -->
    <Sonner
      :theme="'system'"
      :position="'bottom-right'"
      :expand="true"
      :rich-colors="true"
      :close-button="true"
      :duration="4000"
    />
  </TooltipProvider>
</template>

<style>
/* 确保 toast 和 dialog 等组件的正确层级 */
.toaster {
  z-index: 9999;
}

/* 为 dialog 和 drawer 组件提供正确的层级 */
[data-reka-portal] {
  z-index: 9998;
}

/* 确保 tooltip 在合适的层级 */
[data-reka-tooltip-content] {
  z-index: 9997;
}
</style>
