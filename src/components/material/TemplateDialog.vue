<template>
  <Sheet :open="open" @update:open="$emit('update:open', $event)">
    <SheetContent class="w-[800px] sm:max-w-[800px] overflow-y-auto">
      <SheetHeader>
        <SheetTitle>
          {{ template ? '编辑物料模板' : '新建物料模板' }}
        </SheetTitle>
        <SheetDescription>
          基于分类配置创建物料模板，定义基础属性和变体规则
        </SheetDescription>
      </SheetHeader>

      <form @submit.prevent="handleSubmit" class="space-y-6 mt-6">
        <!-- 基本信息 -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">基本信息</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">模板名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入模板名称"
                required
              />
            </div>
            <div class="space-y-2">
              <Label for="code">模板编码 *</Label>
              <Input
                id="code"
                v-model="formData.code"
                placeholder="请输入模板编码"
                required
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="materialType">物料类型 *</Label>
            <Select v-model="formData.materialType" required>
              <SelectTrigger>
                <SelectValue placeholder="选择物料类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="raw_glass">玻璃原片</SelectItem>
                <SelectItem value="profile">型材</SelectItem>
                <SelectItem value="hardware">五金配件</SelectItem>
                <SelectItem value="sealant">密封材料</SelectItem>
                <SelectItem value="chemical">化学材料</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="flex items-center space-x-2">
            <input
              id="isActive"
              v-model="formData.isActive"
              type="checkbox"
              class="rounded border-gray-300"
            />
            <Label for="isActive">启用此模板</Label>
          </div>
        </div>

        <!-- 变体配置 -->
        <div v-if="categoryConfig?.enableVariants" class="space-y-4">
          <h3 class="text-lg font-medium">变体配置</h3>
          
          <div class="flex items-center space-x-2">
            <input
              id="enableVariants"
              v-model="formData.enableVariants"
              type="checkbox"
              class="rounded border-gray-300"
            />
            <Label for="enableVariants">启用变体管理</Label>
          </div>

          <div class="space-y-2">
            <Label for="inventoryLevel">库存管理级别</Label>
            <Select v-model="formData.inventoryManagementLevel">
              <SelectTrigger>
                <SelectValue placeholder="选择库存管理级别" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="template">模板级</SelectItem>
                <SelectItem value="variant">变体级</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <!-- 基础属性配置 -->
        <div v-if="categoryConfig?.baseAttributes.length" class="space-y-4">
          <h3 class="text-lg font-medium">基础属性默认值</h3>
          <div class="grid grid-cols-2 gap-4">
            <div 
              v-for="attr in categoryConfig.baseAttributes"
              :key="attr.id"
              class="space-y-2"
            >
              <Label :for="`base_${attr.id}`">
                {{ attr.displayName }}
                <span v-if="attr.isRequired" class="text-red-500">*</span>
              </Label>
              
              <!-- 选择类型 -->
              <Select 
                v-if="attr.dataType === 'select'"
                v-model="baseAttributeValues[attr.id]"
                :required="attr.isRequired"
              >
                <SelectTrigger>
                  <SelectValue :placeholder="`选择${attr.displayName}`" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem 
                    v-for="option in attr.constraints.find(c => c.type === 'enum')?.enumValues || []"
                    :key="option"
                    :value="option"
                  >
                    {{ option }}
                  </SelectItem>
                </SelectContent>
              </Select>
              
              <!-- 数字类型 -->
              <Input
                v-else-if="attr.dataType === 'integer' || attr.dataType === 'decimal'"
                :id="`base_${attr.id}`"
                v-model="baseAttributeValues[attr.id]"
                type="number"
                :placeholder="`请输入${attr.displayName}`"
                :required="attr.isRequired"
              />
              
              <!-- 文本类型 -->
              <Input
                v-else
                :id="`base_${attr.id}`"
                v-model="baseAttributeValues[attr.id]"
                :placeholder="`请输入${attr.displayName}`"
                :required="attr.isRequired"
              />
            </div>
          </div>
        </div>

        <!-- 变体属性配置预览 -->
        <div v-if="formData.enableVariants && categoryConfig?.variantAttributes.length" class="space-y-4">
          <h3 class="text-lg font-medium">变体属性配置</h3>
          <div class="bg-muted/50 p-4 rounded-lg">
            <p class="text-sm text-muted-foreground mb-3">
              以下变体属性将从分类配置继承，可在变体管理中进一步配置：
            </p>
            <div class="flex flex-wrap gap-2">
              <Badge 
                v-for="attr in categoryConfig.variantAttributes"
                :key="attr.id"
                variant="outline"
              >
                {{ attr.displayName }}
                <span v-if="attr.unit" class="ml-1 text-muted-foreground">
                  ({{ attr.unit.symbol }})
                </span>
              </Badge>
            </div>
          </div>
        </div>

        <!-- 计算字段预览 -->
        <div v-if="categoryConfig?.calculatedFields.length" class="space-y-4">
          <h3 class="text-lg font-medium">自动计算字段</h3>
          <div class="bg-muted/50 p-4 rounded-lg">
            <p class="text-sm text-muted-foreground mb-3">
              以下字段将根据配置的公式自动计算：
            </p>
            <div class="space-y-2">
              <div 
                v-for="field in categoryConfig.calculatedFields"
                :key="field.id"
                class="flex items-center justify-between text-sm"
              >
                <span class="font-medium">{{ field.displayName }}</span>
                <span class="text-muted-foreground font-mono">{{ field.formula }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-2 pt-4 border-t">
          <Button 
            type="button" 
            variant="outline" 
            @click="$emit('update:open', false)"
          >
            取消
          </Button>
          <Button type="submit" :disabled="!isFormValid">
            {{ template ? '更新' : '创建' }}
          </Button>
        </div>
      </form>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { 
  MaterialTemplate, 
  MaterialCategory, 
  MaterialCategoryVariantConfig,
  AttributeValue
} from '@/types/material-variant'

// Props
interface Props {
  open: boolean
  template?: MaterialTemplate | null
  category?: MaterialCategory | null
  categoryConfig?: MaterialCategoryVariantConfig | null
}

const props = withDefaults(defineProps<Props>(), {
  template: null,
  category: null,
  categoryConfig: null
})

// Emits
defineEmits<{
  'update:open': [open: boolean]
  save: [template: MaterialTemplate]
}>()

// 响应式数据
const formData = ref<Partial<MaterialTemplate>>({
  name: '',
  code: '',
  materialType: 'raw_glass',
  enableVariants: false,
  inventoryManagementLevel: 'variant',
  isActive: true
})

const baseAttributeValues = ref<Record<string, any>>({})

// 计算属性
const isFormValid = computed(() => {
  return formData.value.name && formData.value.code && formData.value.materialType
})

// 方法
const handleSubmit = () => {
  if (!isFormValid.value || !props.category) return

  // 构建基础属性值数组
  const baseAttributeValueArray: AttributeValue[] = Object.entries(baseAttributeValues.value)
    .filter(([_, value]) => value !== undefined && value !== '')
    .map(([attrId, value]) => {
      const attr = props.categoryConfig?.baseAttributes.find(a => a.id === attrId)
      return {
        attributeId: attrId,
        attributeName: attr?.name || '',
        value: value,
        unit: attr?.unit?.symbol
      }
    })

  const templateData: MaterialTemplate = {
    id: props.template?.id || `tpl_${Date.now()}`,
    name: formData.value.name!,
    code: formData.value.code!,
    category: props.category,
    materialType: formData.value.materialType as any,
    baseAttributeValues: baseAttributeValueArray,
    variantAttributeConfigs: props.categoryConfig?.variantAttributes || [],
    calculatedFields: props.categoryConfig?.calculatedFields || [],
    enableVariants: formData.value.enableVariants!,
    variants: [],
    inventoryManagementLevel: formData.value.inventoryManagementLevel as 'template' | 'variant',
    codingRule: props.categoryConfig?.codingRules[0],
    constraintRules: props.categoryConfig?.constraintRules || [],
    isActive: formData.value.isActive!,
    createdAt: props.template?.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  $emit('save', templateData)
}

// 监听props变化，初始化表单数据
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    formData.value = {
      name: newTemplate.name,
      code: newTemplate.code,
      materialType: newTemplate.materialType,
      enableVariants: newTemplate.enableVariants,
      inventoryManagementLevel: newTemplate.inventoryManagementLevel,
      isActive: newTemplate.isActive
    }
    
    // 初始化基础属性值
    baseAttributeValues.value = {}
    newTemplate.baseAttributeValues.forEach(attrValue => {
      baseAttributeValues.value[attrValue.attributeId] = attrValue.value
    })
  } else {
    // 重置表单
    formData.value = {
      name: '',
      code: '',
      materialType: 'raw_glass',
      enableVariants: props.categoryConfig?.enableVariants || false,
      inventoryManagementLevel: props.categoryConfig?.inventoryManagementLevel || 'variant',
      isActive: true
    }
    baseAttributeValues.value = {}
  }
}, { immediate: true })

// 监听分类配置变化，初始化默认值
watch(() => props.categoryConfig, (newConfig) => {
  if (newConfig && !props.template) {
    formData.value.enableVariants = newConfig.enableVariants
    formData.value.inventoryManagementLevel = newConfig.inventoryManagementLevel
    
    // 设置基础属性默认值
    newConfig.baseAttributes.forEach(attr => {
      if (attr.defaultValue !== undefined) {
        baseAttributeValues.value[attr.id] = attr.defaultValue
      }
    })
  }
}, { immediate: true })
</script>
