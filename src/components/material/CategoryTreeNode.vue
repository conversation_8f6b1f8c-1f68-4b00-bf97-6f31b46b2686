<template>
  <div class="space-y-1">
    <!-- 当前节点 -->
    <div
      :class="[
        'flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors',
        'hover:bg-accent hover:text-accent-foreground',
        isSelected ? 'bg-accent text-accent-foreground' : '',
        !category.isActive ? 'opacity-60' : ''
      ]"
      @click="handleSelect"
    >
      <div class="flex items-center flex-1 min-w-0">
        <!-- 展开/收起按钮 -->
        <Button
          v-if="hasChildren"
          variant="ghost"
          size="sm"
          class="h-6 w-6 p-0 mr-1"
          @click.stop="toggleExpanded"
        >
          <ChevronRight 
            :class="[
              'h-4 w-4 transition-transform',
              isExpanded ? 'rotate-90' : ''
            ]"
          />
        </Button>
        <div v-else class="w-7" />

        <!-- 分类图标 -->
        <div class="mr-2">
          <Folder 
            v-if="hasChildren" 
            :class="[
              'h-4 w-4',
              isExpanded ? 'text-blue-600' : 'text-muted-foreground'
            ]"
          />
          <FileText v-else class="h-4 w-4 text-muted-foreground" />
        </div>

        <!-- 分类信息 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-2">
            <span class="font-medium truncate">{{ category.name }}</span>
            <Badge v-if="!category.isActive" variant="secondary" class="text-xs">
              禁用
            </Badge>
            <Badge v-if="hasVariantConfig" variant="outline" class="text-xs">
              变体
            </Badge>
          </div>
          <div class="text-xs text-muted-foreground truncate">
            {{ category.code }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" class="h-6 w-6 p-0">
              <MoreHorizontal class="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="handleEdit">
              <Edit class="h-4 w-4 mr-2" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem @click="handleCreateChild">
              <Plus class="h-4 w-4 mr-2" />
              新增子分类
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              @click="handleDelete"
              class="text-destructive focus:text-destructive"
            >
              <Trash2 class="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>

    <!-- 子节点 -->
    <div v-if="isExpanded && hasChildren" class="ml-4 space-y-1">
      <CategoryTreeNode
        v-for="child in filteredChildren"
        :key="child.id"
        :category="child"
        :all-categories="allCategories"
        :selected-category="selectedCategory"
        :search-query="searchQuery"
        @select="$emit('select', $event)"
        @create="$emit('create', $event)"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  ChevronRight,
  Folder,
  FileText,
  MoreHorizontal,
  Edit,
  Plus,
  Trash2
} from 'lucide-vue-next'
import type { MaterialCategory } from '@/types/material-variant'

// Props
interface Props {
  category: MaterialCategory
  allCategories: MaterialCategory[]
  selectedCategory?: MaterialCategory | null
  searchQuery?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedCategory: null,
  searchQuery: ''
})

// Emits
defineEmits<{
  select: [category: MaterialCategory]
  create: [parentCategory?: MaterialCategory]
  edit: [category: MaterialCategory]
  delete: [category: MaterialCategory]
}>()

// 响应式数据
const isExpanded = ref(false)

// 计算属性
const isSelected = computed(() => {
  return props.selectedCategory?.id === props.category.id
})

const hasChildren = computed(() => {
  return props.allCategories.some(cat => cat.parentId === props.category.id)
})

const filteredChildren = computed(() => {
  const children = props.allCategories.filter(cat => cat.parentId === props.category.id)
  
  if (!props.searchQuery) {
    return children
  }
  
  // 搜索时显示匹配的子节点
  return children.filter(child => {
    return matchesSearch(child) || hasMatchingChildren(child)
  })
})

const hasVariantConfig = computed(() => {
  // TODO: 检查是否有变体配置
  return false
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const handleSelect = () => {
  // 如果有子节点，点击时自动展开
  if (hasChildren.value && !isExpanded.value) {
    isExpanded.value = true
  }
  // 发出选择事件
  $emit('select', props.category)
}

const handleEdit = () => {
  $emit('edit', props.category)
}

const handleCreateChild = () => {
  $emit('create', props.category)
}

const handleDelete = () => {
  $emit('delete', props.category)
}

const matchesSearch = (category: MaterialCategory): boolean => {
  const query = props.searchQuery.toLowerCase()
  return (
    category.name.toLowerCase().includes(query) ||
    category.code.toLowerCase().includes(query) ||
    (category.description && category.description.toLowerCase().includes(query))
  )
}

const hasMatchingChildren = (category: MaterialCategory): boolean => {
  const children = props.allCategories.filter(cat => cat.parentId === category.id)
  return children.some(child => matchesSearch(child) || hasMatchingChildren(child))
}

// 搜索时自动展开匹配的节点
if (props.searchQuery && (matchesSearch(props.category) || hasMatchingChildren(props.category))) {
  isExpanded.value = true
}
</script>

<style scoped>
.group:hover .opacity-0 {
  opacity: 1;
}
</style>
