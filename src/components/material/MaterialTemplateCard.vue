<template>
  <Card class="hover:shadow-md transition-shadow">
    <CardHeader>
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="p-2 rounded-lg bg-primary/10">
            <Package class="h-5 w-5 text-primary" />
          </div>
          <div>
            <CardTitle class="text-lg">{{ template.name }}</CardTitle>
            <CardDescription class="flex items-center space-x-2">
              <span class="font-mono text-xs">{{ template.code }}</span>
              <Badge variant="outline" class="text-xs">
                {{ getMaterialTypeText(template.materialType) }}
              </Badge>
            </CardDescription>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <Badge :variant="template.isActive ? 'default' : 'secondary'">
            {{ template.isActive ? '启用' : '禁用' }}
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                <MoreHorizontal class="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="$emit('edit', template)">
                <Edit class="h-4 w-4 mr-2" />
                编辑模板
              </DropdownMenuItem>
              <DropdownMenuItem 
                @click="$emit('manage-variants', template)"
                :disabled="!template.enableVariants"
              >
                <Settings class="h-4 w-4 mr-2" />
                管理变体
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                @click="$emit('delete', template)"
                class="text-destructive focus:text-destructive"
              >
                <Trash2 class="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </CardHeader>
    
    <CardContent class="space-y-4">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="text-muted-foreground">变体管理：</span>
          <span :class="template.enableVariants ? 'text-green-600' : 'text-muted-foreground'">
            {{ template.enableVariants ? '启用' : '禁用' }}
          </span>
        </div>
        <div>
          <span class="text-muted-foreground">变体数量：</span>
          <span class="font-medium">{{ template.variants?.length || 0 }}</span>
        </div>
        <div>
          <span class="text-muted-foreground">库存管理：</span>
          <span>{{ getInventoryLevelText(template.inventoryManagementLevel) }}</span>
        </div>
        <div>
          <span class="text-muted-foreground">更新时间：</span>
          <span>{{ formatDate(template.updatedAt) }}</span>
        </div>
      </div>

      <!-- 属性配置概览 -->
      <div v-if="categoryConfig" class="space-y-3">
        <!-- 基础属性 -->
        <div v-if="categoryConfig.baseAttributes.length > 0">
          <h4 class="text-sm font-medium mb-2 flex items-center">
            <Tag class="h-4 w-4 mr-1" />
            基础属性
          </h4>
          <div class="flex flex-wrap gap-1">
            <Badge 
              v-for="attr in categoryConfig.baseAttributes.slice(0, 5)"
              :key="attr.id"
              variant="secondary"
              class="text-xs"
            >
              {{ attr.displayName }}
            </Badge>
            <Badge 
              v-if="categoryConfig.baseAttributes.length > 5"
              variant="outline"
              class="text-xs"
            >
              +{{ categoryConfig.baseAttributes.length - 5 }}
            </Badge>
          </div>
        </div>

        <!-- 变体属性 -->
        <div v-if="template.enableVariants && categoryConfig.variantAttributes.length > 0">
          <h4 class="text-sm font-medium mb-2 flex items-center">
            <Layers class="h-4 w-4 mr-1" />
            变体属性
          </h4>
          <div class="flex flex-wrap gap-1">
            <Badge 
              v-for="attr in categoryConfig.variantAttributes.slice(0, 5)"
              :key="attr.id"
              variant="outline"
              class="text-xs"
            >
              {{ attr.displayName }}
            </Badge>
            <Badge 
              v-if="categoryConfig.variantAttributes.length > 5"
              variant="outline"
              class="text-xs"
            >
              +{{ categoryConfig.variantAttributes.length - 5 }}
            </Badge>
          </div>
        </div>

        <!-- 计算字段 -->
        <div v-if="categoryConfig.calculatedFields.length > 0">
          <h4 class="text-sm font-medium mb-2 flex items-center">
            <Calculator class="h-4 w-4 mr-1" />
            计算字段
          </h4>
          <div class="flex flex-wrap gap-1">
            <Badge 
              v-for="field in categoryConfig.calculatedFields.slice(0, 3)"
              :key="field.id"
              variant="default"
              class="text-xs"
            >
              {{ field.displayName }}
            </Badge>
            <Badge 
              v-if="categoryConfig.calculatedFields.length > 3"
              variant="outline"
              class="text-xs"
            >
              +{{ categoryConfig.calculatedFields.length - 3 }}
            </Badge>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div v-if="template.aggregatedStats" class="pt-3 border-t">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div class="text-center">
            <div class="text-lg font-semibold text-primary">
              {{ template.aggregatedStats.totalVariants }}
            </div>
            <div class="text-muted-foreground">变体数量</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-green-600">
              {{ template.aggregatedStats.totalStockQuantity }}
            </div>
            <div class="text-muted-foreground">库存数量</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-blue-600">
              ¥{{ formatCurrency(template.aggregatedStats.totalStockValue) }}
            </div>
            <div class="text-muted-foreground">库存价值</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-orange-600">
              {{ template.aggregatedStats.totalAvailableQuantity }}
            </div>
            <div class="text-muted-foreground">可用数量</div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Package,
  MoreHorizontal,
  Edit,
  Settings,
  Trash2,
  Tag,
  Layers,
  Calculator
} from 'lucide-vue-next'
import type { MaterialTemplate, MaterialCategoryVariantConfig } from '@/types/material-variant'

// Props
interface Props {
  template: MaterialTemplate
  categoryConfig?: MaterialCategoryVariantConfig | null
}

defineProps<Props>()

// Emits
defineEmits<{
  edit: [template: MaterialTemplate]
  delete: [template: MaterialTemplate]
  'manage-variants': [template: MaterialTemplate]
}>()

// 辅助方法
const getMaterialTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    raw_glass: '玻璃原片',
    profile: '型材',
    hardware: '五金配件',
    sealant: '密封材料',
    chemical: '化学材料'
  }
  return typeMap[type] || type
}

const getInventoryLevelText = (level?: string) => {
  switch (level) {
    case 'template': return '模板级'
    case 'variant': return '变体级'
    case 'both': return '混合管理'
    default: return '未配置'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN').format(value)
}
</script>
