<template>
  <div class="space-y-1">
    <!-- 搜索框 -->
    <div class="relative mb-4">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input
        v-model="searchQuery"
        placeholder="搜索分类..."
        class="pl-9"
      />
    </div>

    <!-- 树形结构 -->
    <div class="space-y-1">
      <CategoryTreeNode
        v-for="category in filteredRootCategories"
        :key="category.id"
        :category="category"
        :all-categories="categories"
        :selected-category="selectedCategory"
        :search-query="searchQuery"
        @select="$emit('select', $event)"
        @create="$emit('create', $event)"
        @edit="$emit('edit', $event)"
        @delete="$emit('delete', $event)"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="filteredRootCategories.length === 0" class="text-center py-8 text-muted-foreground">
      <Folder class="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p class="text-sm">
        {{ searchQuery ? '未找到匹配的分类' : '暂无物料分类' }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Input } from '@/components/ui/input'
import { Search, Folder } from 'lucide-vue-next'
import CategoryTreeNode from './CategoryTreeNode.vue'
import type { MaterialCategory } from '@/types/material-variant'

// Props
interface Props {
  categories: MaterialCategory[]
  selectedCategory?: MaterialCategory | null
}

const props = withDefaults(defineProps<Props>(), {
  selectedCategory: null
})

// Emits
defineEmits<{
  select: [category: MaterialCategory]
  create: [parentCategory?: MaterialCategory]
  edit: [category: MaterialCategory]
  delete: [category: MaterialCategory]
}>()

// 响应式数据
const searchQuery = ref('')

// 计算属性
const filteredRootCategories = computed(() => {
  const rootCategories = props.categories.filter(cat => !cat.parentId)
  
  if (!searchQuery.value) {
    return rootCategories
  }
  
  // 搜索逻辑：如果分类名称或编码包含搜索词，或其子分类包含搜索词
  return rootCategories.filter(category => {
    return matchesSearch(category) || hasMatchingChildren(category)
  })
})

// 辅助方法
const matchesSearch = (category: MaterialCategory): boolean => {
  const query = searchQuery.value.toLowerCase()
  return (
    category.name.toLowerCase().includes(query) ||
    category.code.toLowerCase().includes(query) ||
    (category.description && category.description.toLowerCase().includes(query))
  )
}

const hasMatchingChildren = (category: MaterialCategory): boolean => {
  const children = props.categories.filter(cat => cat.parentId === category.id)
  return children.some(child => matchesSearch(child) || hasMatchingChildren(child))
}
</script>
