<template>
  <Sheet :open="open" @update:open="$emit('update:open', $event)">
    <SheetContent class="w-[600px] sm:max-w-[600px]">
      <SheetHeader>
        <SheetTitle>
          {{ category ? '编辑物料分类' : '新建物料分类' }}
        </SheetTitle>
        <SheetDescription>
          配置物料分类的基本信息和变体管理设置
        </SheetDescription>
      </SheetHeader>

      <form @submit.prevent="handleSubmit" class="space-y-6 mt-6">
        <!-- 基本信息 -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">基本信息</h3>
          
          <div class="grid grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="name">分类名称 *</Label>
              <Input
                id="name"
                v-model="formData.name"
                placeholder="请输入分类名称"
                required
              />
            </div>
            <div class="space-y-2">
              <Label for="code">分类编码 *</Label>
              <Input
                id="code"
                v-model="formData.code"
                placeholder="请输入分类编码"
                required
              />
            </div>
          </div>

          <div class="space-y-2">
            <Label for="parentId">父分类</Label>
            <Select v-model="formData.parentId">
              <SelectTrigger>
                <SelectValue placeholder="选择父分类（可选）" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">无父分类</SelectItem>
                <SelectItem 
                  v-for="parentCat in availableParentCategories"
                  :key="parentCat.id"
                  :value="parentCat.id"
                >
                  {{ parentCat.name }} ({{ parentCat.code }})
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div class="space-y-2">
            <Label for="description">描述</Label>
            <Textarea
              id="description"
              v-model="formData.description"
              placeholder="请输入分类描述"
              rows="3"
            />
          </div>

          <div class="flex items-center space-x-2">
            <input
              id="isActive"
              v-model="formData.isActive"
              type="checkbox"
              class="rounded border-gray-300"
            />
            <Label for="isActive">启用此分类</Label>
          </div>
        </div>

        <!-- 变体配置 -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">变体管理配置</h3>
          
          <div class="flex items-center space-x-2">
            <input
              id="enableVariants"
              v-model="variantConfig.enableVariants"
              type="checkbox"
              class="rounded border-gray-300"
            />
            <Label for="enableVariants">启用变体管理</Label>
          </div>

          <div v-if="formData.parentId" class="space-y-4">
            <div class="flex items-center space-x-2">
              <input
                id="inheritFromParent"
                v-model="variantConfig.inheritFromParent"
                type="checkbox"
                class="rounded border-gray-300"
              />
              <Label for="inheritFromParent">继承父分类配置</Label>
            </div>

            <div v-if="variantConfig.inheritFromParent" class="flex items-center space-x-2">
              <input
                id="overrideParentConfig"
                v-model="variantConfig.overrideParentConfig"
                type="checkbox"
                class="rounded border-gray-300"
              />
              <Label for="overrideParentConfig">允许覆盖父分类配置</Label>
            </div>
          </div>

          <div v-if="variantConfig.enableVariants" class="space-y-4">
            <div class="space-y-2">
              <Label for="inventoryLevel">库存管理级别</Label>
              <Select v-model="variantConfig.inventoryManagementLevel">
                <SelectTrigger>
                  <SelectValue placeholder="选择库存管理级别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="template">模板级</SelectItem>
                  <SelectItem value="variant">变体级</SelectItem>
                  <SelectItem value="both">混合管理</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-2 pt-4 border-t">
          <Button 
            type="button" 
            variant="outline" 
            @click="$emit('update:open', false)"
          >
            取消
          </Button>
          <Button type="submit" :disabled="!isFormValid">
            {{ category ? '更新' : '创建' }}
          </Button>
        </div>
      </form>
    </SheetContent>
  </Sheet>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { MaterialCategory, MaterialCategoryVariantConfig } from '@/types/material-variant'

// Props
interface Props {
  open: boolean
  category?: MaterialCategory | null
  parentCategories: MaterialCategory[]
}

const props = withDefaults(defineProps<Props>(), {
  category: null
})

// Emits
defineEmits<{
  'update:open': [open: boolean]
  save: [category: MaterialCategory]
}>()

// 响应式数据
const formData = ref<Partial<MaterialCategory>>({
  name: '',
  code: '',
  parentId: '',
  description: '',
  isActive: true
})

const variantConfig = ref<Partial<MaterialCategoryVariantConfig>>({
  enableVariants: false,
  inheritFromParent: true,
  overrideParentConfig: false,
  inventoryManagementLevel: 'variant'
})

// 计算属性
const availableParentCategories = computed(() => {
  // 排除当前分类及其子分类，避免循环引用
  if (!props.category) {
    return props.parentCategories
  }
  
  return props.parentCategories.filter(cat => {
    return cat.id !== props.category?.id && !isDescendant(cat, props.category)
  })
})

const isFormValid = computed(() => {
  return formData.value.name && formData.value.code
})

// 方法
const isDescendant = (category: MaterialCategory, ancestor: MaterialCategory): boolean => {
  if (category.parentId === ancestor.id) {
    return true
  }
  const parent = props.parentCategories.find(cat => cat.id === category.parentId)
  return parent ? isDescendant(parent, ancestor) : false
}

const handleSubmit = () => {
  if (!isFormValid.value) return

  const categoryData: MaterialCategory = {
    id: props.category?.id || `cat_${Date.now()}`,
    name: formData.value.name!,
    code: formData.value.code!,
    parentId: formData.value.parentId || undefined,
    description: formData.value.description,
    isActive: formData.value.isActive!,
    variantConfig: variantConfig.value.enableVariants ? {
      id: `config_${Date.now()}`,
      categoryId: props.category?.id || `cat_${Date.now()}`,
      enableVariants: variantConfig.value.enableVariants!,
      inheritFromParent: variantConfig.value.inheritFromParent!,
      overrideParentConfig: variantConfig.value.overrideParentConfig!,
      inventoryManagementLevel: variantConfig.value.inventoryManagementLevel as 'template' | 'variant' | 'both',
      baseAttributes: [],
      variantAttributes: [],
      calculatedFields: [],
      codingRules: [],
      constraintRules: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } : undefined
  }

  $emit('save', categoryData)
}

// 监听props变化，初始化表单数据
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    formData.value = {
      name: newCategory.name,
      code: newCategory.code,
      parentId: newCategory.parentId || '',
      description: newCategory.description,
      isActive: newCategory.isActive
    }
    
    if (newCategory.variantConfig) {
      variantConfig.value = {
        enableVariants: newCategory.variantConfig.enableVariants,
        inheritFromParent: newCategory.variantConfig.inheritFromParent,
        overrideParentConfig: newCategory.variantConfig.overrideParentConfig,
        inventoryManagementLevel: newCategory.variantConfig.inventoryManagementLevel
      }
    }
  } else {
    // 重置表单
    formData.value = {
      name: '',
      code: '',
      parentId: '',
      description: '',
      isActive: true
    }
    variantConfig.value = {
      enableVariants: false,
      inheritFromParent: true,
      overrideParentConfig: false,
      inventoryManagementLevel: 'variant'
    }
  }
}, { immediate: true })
</script>
