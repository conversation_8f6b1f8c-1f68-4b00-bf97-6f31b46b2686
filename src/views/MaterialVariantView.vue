<template>
  <div class="flex h-full">
    <!-- 左侧：物料分类树 -->
    <div class="w-80 border-r bg-muted/10">
      <div class="p-4 border-b">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">物料分类</h2>
          <Button size="sm" @click="showCategoryDialog = true">
            <Plus class="h-4 w-4 mr-1" />
            新增
          </Button>
        </div>
      </div>
      <div class="p-2">
        <MaterialCategoryTree
          :categories="materialCategories"
          :selected-category="selectedCategory"
          @select="onCategorySelect"
          @create="onCategoryCreate"
          @edit="onCategoryEdit"
          @delete="onCategoryDelete"
        />
      </div>
    </div>

    <!-- 右侧：物料模板和变体管理 -->
    <div class="flex-1 flex flex-col">
      <!-- 顶部工具栏 -->
      <div class="p-4 border-b bg-background">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/inventory">库存管理</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>物料变体管理</BreadcrumbPage>
                </BreadcrumbItem>
                <BreadcrumbSeparator v-if="selectedCategory" />
                <BreadcrumbItem v-if="selectedCategory">
                  <BreadcrumbPage>{{ selectedCategory.name }}</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <div class="flex items-center space-x-2">
            <Button variant="outline" size="sm" @click="refreshData">
              <RotateCcw class="h-4 w-4 mr-1" />
              刷新
            </Button>
            <Button 
              size="sm" 
              @click="showTemplateDialog = true"
              :disabled="!selectedCategory"
            >
              <Plus class="h-4 w-4 mr-1" />
              新建模板
            </Button>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="flex-1 p-4">
        <div v-if="!selectedCategory" class="flex items-center justify-center h-full text-muted-foreground">
          <div class="text-center">
            <Package class="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p class="text-lg mb-2">请选择物料分类</p>
            <p class="text-sm">选择左侧的物料分类以查看对应的物料模板和变体</p>
          </div>
        </div>

        <div v-else class="space-y-6">
          <!-- 分类信息卡片 -->
          <Card>
            <CardHeader>
              <div class="flex items-center justify-between">
                <div>
                  <CardTitle class="flex items-center">
                    <Folder class="h-5 w-5 mr-2" />
                    {{ selectedCategory.name }}
                  </CardTitle>
                  <CardDescription>
                    {{ selectedCategory.description || '暂无描述' }}
                  </CardDescription>
                </div>
                <Badge :variant="selectedCategory.isActive ? 'default' : 'secondary'">
                  {{ selectedCategory.isActive ? '启用' : '禁用' }}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-muted-foreground">分类编码：</span>
                  <span class="font-mono">{{ selectedCategory.code }}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">变体管理：</span>
                  <span>{{ categoryConfig?.enableVariants ? '启用' : '禁用' }}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">库存粒度：</span>
                  <span>{{ getInventoryLevelText(categoryConfig?.inventoryManagementLevel) }}</span>
                </div>
                <div>
                  <span class="text-muted-foreground">模板数量：</span>
                  <span>{{ materialTemplates.length }}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- 物料模板列表 -->
          <Card>
            <CardHeader>
              <CardTitle>物料模板</CardTitle>
              <CardDescription>
                管理该分类下的物料模板和变体
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div v-if="materialTemplates.length === 0" class="text-center py-8 text-muted-foreground">
                <Package class="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>暂无物料模板</p>
                <p class="text-sm">点击"新建模板"开始创建</p>
              </div>
              <div v-else class="space-y-4">
                <MaterialTemplateCard
                  v-for="template in materialTemplates"
                  :key="template.id"
                  :template="template"
                  :category-config="categoryConfig"
                  @edit="onTemplateEdit"
                  @delete="onTemplateDelete"
                  @manage-variants="onManageVariants"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>

  <!-- 分类创建/编辑对话框 -->
  <CategoryDialog
    v-model:open="showCategoryDialog"
    :category="editingCategory"
    :parent-categories="materialCategories"
    @save="onCategorySave"
  />

  <!-- 模板创建/编辑对话框 -->
  <TemplateDialog
    v-model:open="showTemplateDialog"
    :template="editingTemplate"
    :category="selectedCategory"
    :category-config="categoryConfig"
    @save="onTemplateSave"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Plus, RotateCcw, Package, Folder } from 'lucide-vue-next'

// 导入业务组件（稍后创建）
import MaterialCategoryTree from '@/components/material/MaterialCategoryTree.vue'
import MaterialTemplateCard from '@/components/material/MaterialTemplateCard.vue'
import CategoryDialog from '@/components/material/CategoryDialog.vue'
import TemplateDialog from '@/components/material/TemplateDialog.vue'

// 导入类型定义和服务
import type {
  MaterialCategory,
  MaterialCategoryVariantConfig,
  MaterialTemplate
} from '@/types/material-variant'
import { materialVariantService } from '@/services/materialVariantService'

// 响应式数据
const materialCategories = ref<MaterialCategory[]>([])
const selectedCategory = ref<MaterialCategory | null>(null)
const categoryConfig = ref<MaterialCategoryVariantConfig | null>(null)
const materialTemplates = ref<MaterialTemplate[]>([])

// 对话框状态
const showCategoryDialog = ref(false)
const showTemplateDialog = ref(false)
const editingCategory = ref<MaterialCategory | null>(null)
const editingTemplate = ref<MaterialTemplate | null>(null)

// 计算属性
const getInventoryLevelText = (level?: string) => {
  switch (level) {
    case 'template': return '模板级'
    case 'variant': return '变体级'
    case 'both': return '混合管理'
    default: return '未配置'
  }
}

// 事件处理
const onCategorySelect = async (category: MaterialCategory) => {
  selectedCategory.value = category
  await loadCategoryConfig(category.id)
  await loadMaterialTemplates(category.id)
}

const onCategoryCreate = () => {
  editingCategory.value = null
  showCategoryDialog.value = true
}

const onCategoryEdit = (category: MaterialCategory) => {
  editingCategory.value = category
  showCategoryDialog.value = true
}

const onCategoryDelete = async (category: MaterialCategory) => {
  // TODO: 实现删除逻辑
  console.log('删除分类:', category)
}

const onCategorySave = async (category: MaterialCategory) => {
  try {
    await materialVariantService.saveMaterialCategory(category)
    showCategoryDialog.value = false
    await loadMaterialCategories()
  } catch (error) {
    console.error('保存分类失败:', error)
    // TODO: 显示错误提示
  }
}

const onTemplateEdit = (template: MaterialTemplate) => {
  editingTemplate.value = template
  showTemplateDialog.value = true
}

const onTemplateDelete = async (template: MaterialTemplate) => {
  // TODO: 实现删除逻辑
  console.log('删除模板:', template)
}

const onTemplateSave = async (template: MaterialTemplate) => {
  try {
    await materialVariantService.saveMaterialTemplate(template)
    showTemplateDialog.value = false
    if (selectedCategory.value) {
      await loadMaterialTemplates(selectedCategory.value.id)
    }
  } catch (error) {
    console.error('保存模板失败:', error)
    // TODO: 显示错误提示
  }
}

const onManageVariants = (template: MaterialTemplate) => {
  // TODO: 跳转到变体管理页面
  console.log('管理变体:', template)
}

const refreshData = async () => {
  await loadMaterialCategories()
  if (selectedCategory.value) {
    await loadCategoryConfig(selectedCategory.value.id)
    await loadMaterialTemplates(selectedCategory.value.id)
  }
}

// 数据加载方法
const loadMaterialCategories = async () => {
  try {
    materialCategories.value = await materialVariantService.loadMaterialCategories()
  } catch (error) {
    console.error('加载物料分类失败:', error)
    // TODO: 显示错误提示
  }
}

const loadCategoryConfig = async (categoryId: string) => {
  try {
    categoryConfig.value = await materialVariantService.loadCategoryVariantConfig(categoryId)
  } catch (error) {
    console.error('加载分类配置失败:', error)
    // TODO: 显示错误提示
  }
}

const loadMaterialTemplates = async (categoryId: string) => {
  try {
    materialTemplates.value = await materialVariantService.loadMaterialTemplates(categoryId)
  } catch (error) {
    console.error('加载物料模板失败:', error)
    // TODO: 显示错误提示
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadMaterialCategories()
})
</script>
