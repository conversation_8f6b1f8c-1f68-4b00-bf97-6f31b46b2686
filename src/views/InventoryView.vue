<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-3xl font-bold tracking-tight">库存管理</h1>
      <div class="flex items-center space-x-2">
        <Button variant="outline" @click="$router.push('/inventory/material-variants')">
          <Settings class="mr-2 h-4 w-4" />
          物料变体管理
        </Button>
        <Button>
          <Plus class="mr-2 h-4 w-4" />
          新增物料
        </Button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- 快速导航卡片 -->
      <Card class="cursor-pointer hover:shadow-md transition-shadow" @click="$router.push('/inventory/material-variants')">
        <CardHeader>
          <CardTitle class="flex items-center">
            <Package class="h-5 w-5 mr-2" />
            物料变体管理
          </CardTitle>
          <CardDescription>
            管理物料分类、模板和变体配置
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-primary">配置中心</div>
          <p class="text-sm text-muted-foreground">点击进入物料变体管理</p>
        </CardContent>
      </Card>

      <!-- 库存概览卡片 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <BarChart3 class="h-5 w-5 mr-2" />
            库存概览
          </CardTitle>
          <CardDescription>
            当前库存状态统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-green-600">1,234</div>
          <p class="text-sm text-muted-foreground">总库存数量</p>
        </CardContent>
      </Card>

      <!-- 预警信息卡片 -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center">
            <AlertTriangle class="h-5 w-5 mr-2" />
            库存预警
          </CardTitle>
          <CardDescription>
            需要关注的库存异常
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-orange-600">5</div>
          <p class="text-sm text-muted-foreground">低库存预警</p>
        </CardContent>
      </Card>
    </div>

    <Card>
      <CardHeader>
        <CardTitle>物料库存</CardTitle>
        <CardDescription>
          管理玻璃原片、型材等物料的库存信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div class="h-[400px] flex items-center justify-center text-muted-foreground">
          库存数据表格区域
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Settings, Package, BarChart3, AlertTriangle } from 'lucide-vue-next'
</script>