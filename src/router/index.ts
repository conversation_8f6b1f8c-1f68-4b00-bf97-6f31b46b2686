import { createRouter, createWebHistory } from 'vue-router'
import DashboardView from '../views/DashboardView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: DashboardView,
    },
    {
      path: '/crm',
      name: 'crm',
      component: () => import('../views/CrmView.vue'),
    },
    {
      path: '/inventory',
      name: 'inventory',
      component: () => import('../views/InventoryView.vue'),
    },
    {
      path: '/inventory/material-variants',
      name: 'material-variants',
      component: () => import('../views/MaterialVariantView.vue'),
    },
    {
      path: '/mes',
      name: 'mes',
      component: () => import('../views/MesView.vue'),
    },
    {
      path: '/procurement',
      name: 'procurement',
      component: () => import('../views/ProcurementView.vue'),
    },
    {
      path: '/quality',
      name: 'quality',
      component: () => import('../views/QualityView.vue'),
    },
    {
      path: '/user-test',
      name: 'user-test',
      component: () => import('../views/UserTestView.vue'),
    },
    {
      path: '/store-test',
      name: 'store-test',
      component: () => import('../views/StoreTestView.vue'),
    },
  ],
})

export default router
