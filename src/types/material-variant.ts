/**
 * 玻璃行业物料变体数据模型
 * 基于 ODOO 数据结构，结合玻璃深加工行业特性
 */

// 自动计算字段配置
export interface CalculatedFieldConfig {
  id: string;
  name: string;
  displayName: string;
  formula: string;                   // 计算公式：如 "width * height / 1000000"
  dependencies: string[];            // 依赖的属性字段
  unit?: UnitDefinition;
  precision: number;                 // 小数位数
  isDisplayInList: boolean;
  updateTrigger: 'onChange' | 'onSave' | 'manual';
  isActive: boolean;
}

// 物料编码规则
export interface MaterialCodingRule {
  id: string;
  categoryId: string;
  name: string;
  template: string;                  // 编码模板：如 "GLASS_{type}_{thickness}MM_{color}_{width}x{height}_{sequence}"
  segments: CodingSegment[];
  separator: string;                 // 分隔符：如 "_" 或 "-"
  autoGenerate: boolean;
  isActive: boolean;
  priority: number;                  // 优先级（支持多套规则时使用）
}

// 编码段配置
export interface CodingSegment {
  id: string;
  name: string;
  type: 'fixed' | 'attribute' | 'sequence' | 'calculated';
  value?: string;                    // 固定值
  attributeId?: string;              // 关联的属性ID
  format?: string;                   // 格式化规则：如 'uppercase', 'integer', 'code_mapping'
  length?: number;                   // 长度限制
  padChar?: string;                  // 填充字符
  mapping?: Record<string, string>;  // 值映射：如 {"透明": "CLR", "茶色": "BRZ"}
}

// 变体约束规则（阻止保存模式）
export interface VariantConstraintRule {
  id: string;
  name: string;
  categoryId: string;
  ruleType: 'validation' | 'dependency' | 'calculation' | 'business';
  condition: ConstraintCondition;
  action: ConstraintAction;
  priority: number;
  isActive: boolean;
  errorLevel: 'error' | 'warning';   // 根据你的要求：主要使用 'error' 阻止保存
}

// 约束条件
export interface ConstraintCondition {
  type: 'simple' | 'complex';
  expression: string;                // 如：thickness > 12 AND (width > 2000 OR height > 2000)
  attributes: string[];
}

// 约束动作
export interface ConstraintAction {
  type: 'block' | 'warn' | 'auto_adjust' | 'calculate';
  message: string;
  adjustments?: AttributeAdjustment[];
}

// 属性调整
export interface AttributeAdjustment {
  attributeId: string;
  newValue: string | number | boolean;
  reason: string;
}

// 基础类型定义（保持向后兼容，但标记为废弃）
/** @deprecated 使用 CategoryAttributeConfig 替代 */
export interface BaseAttribute {
  id: string;
  name: string; // "厚度"、"颜色"、"等级"
  type: 'number' | 'text' | 'select';
  unit?: string; // "mm"
  options?: string[]; // ["透明", "茶色", "蓝色"]
  isRequired: boolean;
}

/** @deprecated 使用 CategoryAttributeConfig 替代 */
export interface VariantAttribute {
  id: string;
  name: string; // "宽度"、"高度"、"长度"
  type: 'number';
  unit: string; // "mm"
  minValue?: number;
  maxValue?: number;
  isRequired: boolean;
}

export interface AttributeValue {
  attributeId: string;
  attributeName: string;
  value: string | number;
  unit?: string;
}

// 物料分类
export interface MaterialCategory {
  id: string;
  name: string;
  code: string;
  parentId?: string;
  description?: string;
  isActive: boolean;
  // 新增：变体配置
  variantConfig?: MaterialCategoryVariantConfig;
}

// 物料分类变体配置（支持灵活覆盖继承）
export interface MaterialCategoryVariantConfig {
  id: string;
  categoryId: string;
  enableVariants: boolean;
  inheritFromParent: boolean;        // 是否继承父分类配置
  overrideParentConfig: boolean;     // 是否覆盖父分类配置
  inventoryManagementLevel: 'template' | 'variant' | 'both'; // 库存管理粒度
  baseAttributes: CategoryAttributeConfig[];     // 基础属性配置
  variantAttributes: CategoryAttributeConfig[];  // 变体属性配置
  calculatedFields: CalculatedFieldConfig[];     // 自动计算字段
  codingRules: MaterialCodingRule[];             // 编码规则
  constraintRules: VariantConstraintRule[];      // 约束规则
  createdAt: string;
  updatedAt: string;
}

// 分类属性配置（支持继承和覆盖）
export interface CategoryAttributeConfig {
  id: string;
  name: string;
  displayName: string;
  dataType: AttributeDataType;
  unit?: UnitDefinition;
  isRequired: boolean;
  isInherited: boolean;              // 是否从父分类继承
  canOverride: boolean;              // 子分类是否可以覆盖
  isSearchable: boolean;
  isDisplayInList: boolean;
  sortOrder: number;
  constraints: AttributeConstraint[];
  defaultValue?: string | number | boolean | string[];
}

// 数据类型枚举（扩展原有类型）
export type AttributeDataType =
  | 'integer'      // 整数：厚度、数量
  | 'decimal'      // 小数：长度、重量
  | 'text'         // 文本：备注、描述
  | 'select'       // 单选：颜色、等级
  | 'multiselect'  // 多选：认证标准
  | 'boolean'      // 布尔：是否钢化
  | 'date'         // 日期：生产日期
  | 'dimension';   // 尺寸：宽×高×厚

// 单位定义
export interface UnitDefinition {
  id: string;
  name: string;        // "毫米"
  symbol: string;      // "mm"
  category: string;    // "length", "area", "weight"
  conversionFactor?: number; // 转换系数（相对于基础单位）
}

// 属性约束
export interface AttributeConstraint {
  id: string;
  type: 'range' | 'enum' | 'regex' | 'custom' | 'dependency';
  minValue?: number;
  maxValue?: number;
  enumValues?: string[];
  regexPattern?: string;
  customValidator?: string;          // 自定义验证函数名
  dependsOn?: string[];              // 依赖的其他属性
  errorMessage: string;
  isBlocking: boolean;               // 是否阻止保存（根据你的要求：阻止保存）
}

// 供应商信息
export interface Supplier {
  id: string;
  name: string;
  code: string;
  contactInfo: {
    phone?: string;
    email?: string;
    address?: string;
  };
  isActive: boolean;
}

// 物料模板接口（更新以支持新的配置结构）
export interface MaterialTemplate {
  id: string;
  name: string; // "浮法玻璃"
  code: string; // "GLASS_FLOAT"
  category: MaterialCategory;
  materialType: 'raw_glass' | 'profile' | 'hardware' | 'sealant' | 'chemical';

  // 新的属性配置（从分类继承并可覆盖）
  baseAttributeValues: AttributeValue[];     // 基础属性的默认值
  variantAttributeConfigs: CategoryAttributeConfig[]; // 变体属性配置（可覆盖分类配置）
  calculatedFields: CalculatedFieldConfig[]; // 计算字段配置

  // 变体管理
  enableVariants: boolean;                   // 是否启用变体（从分类继承）
  variants: MaterialVariant[];               // 具体的变体列表

  // 库存管理配置
  inventoryManagementLevel: 'template' | 'variant'; // 库存管理级别

  // 编码和约束
  codingRule?: MaterialCodingRule;           // 使用的编码规则
  constraintRules: VariantConstraintRule[];  // 约束规则

  // 汇总统计字段（支持按主物料汇总）
  aggregatedStats?: MaterialTemplateAggregatedStats;

  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 物料模板汇总统计（支持按主物料汇总，再下钻到变体）
export interface MaterialTemplateAggregatedStats {
  templateId: string;
  totalVariants: number;
  totalStockQuantity: number;
  totalStockValue: number;
  totalReservedQuantity: number;
  totalAvailableQuantity: number;

  // 玻璃行业特色汇总
  totalArea?: number;                        // 总面积（平方米）
  totalWeight?: number;                      // 总重量（千克）
  totalLength?: number;                      // 总长度（米，用于型材）

  // 按变体的明细统计
  variantStats: MaterialVariantStats[];

  lastUpdated: string;
}

// 变体统计明细
export interface MaterialVariantStats {
  variantId: string;
  sku: string;
  displayName: string;
  stockQuantity: number;
  stockValue: number;
  reservedQuantity: number;
  availableQuantity: number;
  area?: number;
  weight?: number;
  length?: number;
}

// 物料变体基础接口（更新以支持新的业务需求）
export interface MaterialVariant {
  id: string;
  templateId: string;
  sku: string; // "GLASS_FLOAT_6MM_CLEAR_3300x2140"
  displayName: string; // "6mm透明浮法玻璃 3300x2140mm"

  // 属性值（基于新的配置结构）
  baseAttributeValues: AttributeValue[]; // 厚度:6mm, 颜色:透明, 等级:优等品
  variantAttributeValues: AttributeValue[]; // 宽度:3300mm, 高度:2140mm
  calculatedFieldValues: CalculatedFieldValue[]; // 面积、重量等计算字段

  // 成本和供应商信息
  cost: number;
  supplier: Supplier;
  leadTime: number; // 交货周期（天）

  // 库存信息（根据配置决定是否在变体级别管理）
  stockQuantity: number;
  reservedQuantity: number;
  availableQuantity: number;

  // 约束验证状态
  validationStatus: VariantValidationStatus;

  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 计算字段值
export interface CalculatedFieldValue {
  fieldId: string;
  fieldName: string;
  value: number;
  unit?: UnitDefinition;
  calculatedAt: string;
}

// 变体验证状态（支持阻止保存的约束验证）
export interface VariantValidationStatus {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  lastValidatedAt: string;
}

// 验证错误（阻止保存）
export interface ValidationError {
  ruleId: string;
  ruleName: string;
  message: string;
  affectedAttributes: string[];
  severity: 'critical' | 'high';
}

// 验证警告（不阻止保存）
export interface ValidationWarning {
  ruleId: string;
  ruleName: string;
  message: string;
  affectedAttributes: string[];
  suggestion?: string;
}

// 玻璃原片物料变体接口
export interface GlassSheetVariant extends MaterialVariant {
  thickness: number; // mm
  color: string; // "透明"、"茶色"、"蓝色"
  grade: string; // "优等品"、"一等品"
  width: number; // mm
  height: number; // mm
  glassType: 'float' | 'ultra_clear' | 'tinted' | 'reflective';
  surfaceQuality: 'standard' | 'premium';
}

// 型材物料变体接口
export interface ProfileVariant extends MaterialVariant {
  crossSection: string; // "50x30mm"
  material: string; // "铝合金"、"塑钢"
  color: string; // "银白色"、"香槟色"
  length: number; // mm
  wallThickness: number; // mm
  surfaceTreatment: 'anodized' | 'powder_coated' | 'electrophoresis';
}

// 库存位置
export interface StockLocation {
  id: string;
  name: string;
  code: string;
  type: 'warehouse' | 'production' | 'quality' | 'shipping';
  parentId?: string;
  isActive: boolean;
}

// 物料变体库存管理（支持按物料类型配置库存粒度）
export interface MaterialVariantStock {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  locationId: string;
  location: StockLocation;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  unitCost: number;
  totalValue: number;
  lastMovementDate: string;
  reorderPoint: number; // 物料变体级别的安全库存
  maxStock: number; // 物料变体级别的最大库存
  lotNumbers: string[]; // 批次号
  expiryDate?: string; // 过期日期（如适用）

  // 新增：支持汇总统计
  aggregationLevel: 'variant' | 'template'; // 库存管理粒度
  parentTemplateId?: string; // 关联的物料模板ID（用于汇总）
}

// 物料模板级别库存汇总（支持按主物料汇总，再下钻到变体）
export interface MaterialTemplateStockSummary {
  templateId: string;
  templateName: string;
  templateCode: string;

  // 汇总数据
  totalQuantity: number;
  totalValue: number;
  totalReservedQuantity: number;
  totalAvailableQuantity: number;

  // 玻璃行业特色汇总字段
  totalArea?: number; // 总面积（平方米）
  totalWeight?: number; // 总重量（千克）
  totalLength?: number; // 总长度（米，用于型材）

  // 变体明细（支持下钻）
  variantStocks: MaterialVariantStock[];

  // 库存预警统计
  lowStockVariants: number; // 低库存变体数量
  overstockVariants: number; // 超储变体数量

  lastUpdated: string;
}

// 物料变体库存移动记录
export interface MaterialVariantStockMove {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  quantity: number;
  sourceLocationId: string;
  destinationLocationId: string;
  moveType: 'receipt' | 'delivery' | 'internal' | 'adjustment' | 'scrap';
  reference: string;
  state: 'draft' | 'confirmed' | 'done' | 'cancelled';
  unitCost: number;
  totalCost: number;
  scheduledDate: string;
  effectiveDate?: string;
  relatedOrderId?: string;
  cuttingPlanId?: string; // 关联的切割计划
  createdAt: string;
  updatedAt: string;
}

// 余料物料变体库存
export interface WasteMaterialVariantStock {
  id: string;
  originalMaterialVariantId: string;
  originalMaterialVariant: MaterialVariant;
  currentDimensions: Dimensions;
  remainingArea: number; // 对于玻璃
  remainingLength: number; // 对于型材
  quality: 'good' | 'damaged' | 'unusable';
  locationId: string;
  createdDate: string;
  lastUsedDate?: string;
  potentialUses: PotentialUse[]; // 可能的用途匹配
}

// 尺寸信息
export interface Dimensions {
  width?: number; // mm
  height?: number; // mm
  length?: number; // mm
  thickness?: number; // mm
}

// 潜在用途匹配
export interface PotentialUse {
  orderItemId: string;
  requiredDimensions: Dimensions;
  matchScore: number; // 匹配度评分 (0-100)
  wasteAfterUse: number;
}

// 物料变体补货规则
export interface MaterialVariantReorderRule {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  minQuantity: number;
  maxQuantity: number;
  reorderQuantity: number;
  leadTime: number;
  supplierId: string;
  isActive: boolean;
  seasonalAdjustment?: SeasonalAdjustment[];
  createdAt: string;
  updatedAt: string;
}

// 季节性调整
export interface SeasonalAdjustment {
  month: number; // 1-12
  adjustmentFactor: number; // 季节性调整系数
}

// 物料变体库存预警
export interface MaterialVariantStockAlert {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  alertType: 'low_stock' | 'overstock' | 'no_stock' | 'expiring' | 'slow_moving';
  currentQuantity: number;
  thresholdQuantity: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  createdDate: string;
  isResolved: boolean;
  resolvedDate?: string;
}

// 物料变体需求预测
export interface MaterialVariantDemandForecast {
  id: string;
  materialVariantId: string;
  materialVariant: MaterialVariant;
  forecastPeriod: 'weekly' | 'monthly' | 'quarterly';
  startDate: string;
  endDate: string;
  predictedDemand: number;
  confidence: number; // 预测置信度 (0-100)
  historicalData: HistoricalDemandData[];
  createdAt: string;
}

// 历史需求数据
export interface HistoricalDemandData {
  period: string; // 时间段
  actualDemand: number;
  factors: string[]; // 影响因素
}

// 物料变体优化相关类型
export interface MaterialVariantOptimizationInput {
  requiredPieces: RequiredPiece[]; // 客户订单需要的玻璃片
  availableMaterialVariants: GlassSheetVariant[]; // 可用的玻璃原片物料变体
  optimizationGoal: 'minimize_waste' | 'minimize_cost' | 'minimize_variants' | 'balanced';
  constraints: OptimizationConstraints;
}

// 需求片段
export interface RequiredPiece {
  id: string;
  width: number;
  height: number;
  thickness: number;
  color: string;
  grade: string;
  quantity: number;
  orderItemId: string;
  allowRotation: boolean;
}

// 优化约束条件
export interface OptimizationConstraints {
  maxVariantsToUse: number; // 最多使用的变体数量
  minUtilizationRate: number; // 最低利用率要求
  preferredVariants: string[]; // 优先使用的变体ID
  excludedVariants: string[]; // 排除的变体ID
}

// 物料变体优化结果
export interface MaterialVariantOptimizationResult {
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  overallUtilizationRate: number;
  variantCuttingPlans: VariantCuttingPlan[];
  alternativeOptions: AlternativeOption[];
}

// 选中的物料变体
export interface SelectedMaterialVariant {
  materialVariantId: string;
  materialVariant: GlassSheetVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteArea: number;
}

// 变体切割计划
export interface VariantCuttingPlan {
  materialVariantId: string;
  sheetIndex: number; // 第几张原片
  pieces: CuttingPiece[];
  wasteAreas: WasteArea[];
}

// 切割片段
export interface CuttingPiece {
  pieceId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  orderItemId: string;
  requiredPieceId: string;
}

// 废料区域
export interface WasteArea {
  x: number;
  y: number;
  width: number;
  height: number;
  area: number;
  isReusable: boolean; // 是否可作为余料重复利用
}

// 替代方案
export interface AlternativeOption {
  id: string;
  description: string;
  selectedMaterialVariants: SelectedMaterialVariant[];
  totalCost: number;
  totalWasteArea: number;
  utilizationRate: number;
  pros: string[];
  cons: string[];
}

// 型材切割优化相关类型
export interface ProfileMaterialOptimizationInput {
  requiredLengths: RequiredLength[];
  availableProfileMaterialVariants: ProfileVariant[];
  optimizationGoal: 'minimize_waste' | 'minimize_cost';
}

// 需求长度
export interface RequiredLength {
  id: string;
  length: number;
  quantity: number;
  orderItemId: string;
  profileTemplateId: string;
}

// 型材物料优化结果
export interface ProfileMaterialOptimizationResult {
  selectedProfileMaterialVariants: SelectedProfileMaterialVariant[];
  cuttingPlans: ProfileCuttingPlan[];
  totalWasteLength: number;
  utilizationRate: number;
}

// 选中的型材物料变体
export interface SelectedProfileMaterialVariant {
  materialVariantId: string;
  materialVariant: ProfileVariant;
  quantityUsed: number;
  totalCost: number;
  utilizationRate: number;
  wasteLength: number;
}

// 型材切割计划
export interface ProfileCuttingPlan {
  materialVariantId: string;
  profileIndex: number;
  cuts: ProfileCut[];
  wasteLength: number;
}

// 型材切割
export interface ProfileCut {
  cutId: string;
  startPosition: number;
  length: number;
  orderItemId: string;
  requiredLengthId: string;
}

// 物料变体组合方案
export interface MaterialVariantCombination {
  id: string;
  materialVariants: MaterialVariant[];
  requirements: RequiredPiece[] | RequiredLength[];
  estimatedCost: number;
  estimatedWaste: number;
  utilizationRate: number;
  complexity: number; // 方案复杂度评分
}

// 物料变体评估结果
export interface MaterialVariantEvaluationResult {
  combinationId: string;
  totalCost: number;
  totalWaste: number;
  utilizationRate: number;
  materialVariantCount: number;
  score: number; // 综合评分
  cuttingPlans: (VariantCuttingPlan | ProfileCuttingPlan)[];
}

// 物料变体使用统计
export interface MaterialVariantUsageStats {
  materialVariantId: string;
  materialVariant: MaterialVariant;
  totalUsed: number;
  totalWaste: number;
  averageUtilizationRate: number;
  usageFrequency: number;
  lastUsedDate: string;
  costEfficiency: number; // 成本效率评分
}

// 玻璃行业典型约束规则预定义
export interface GlassIndustryConstraintRules {
  // 大板玻璃厚度限制
  largePanelThicknessRule: VariantConstraintRule;
  // 钢化玻璃尺寸限制
  temperedGlassSizeRule: VariantConstraintRule;
  // 夹胶玻璃重量计算
  laminatedGlassWeightRule: VariantConstraintRule;
  // 中空玻璃间隔条限制
  insulatingGlassSpacerRule: VariantConstraintRule;
  // 型材长度与截面关系
  profileLengthSectionRule: VariantConstraintRule;
}

// 约束规则执行引擎接口
export interface ConstraintRuleEngine {
  validateVariant(variant: MaterialVariant, rules: VariantConstraintRule[]): VariantValidationStatus;
  executeCalculation(variant: MaterialVariant, calculatedFields: CalculatedFieldConfig[]): CalculatedFieldValue[];
  generateSKU(variant: MaterialVariant, codingRule: MaterialCodingRule): string;
  checkBusinessConstraints(variant: MaterialVariant, categoryConfig: MaterialCategoryVariantConfig): ValidationError[];
}

// 物料变体配置向导（简化用户操作）
export interface MaterialVariantConfigWizard {
  step: 'category' | 'template' | 'attributes' | 'variants' | 'validation';
  categorySelection: MaterialCategory;
  templateConfiguration: MaterialTemplate;
  attributeConfiguration: CategoryAttributeConfig[];
  variantGeneration: VariantGenerationConfig;
  validationResults: VariantValidationStatus[];
}

// 变体生成配置
export interface VariantGenerationConfig {
  generateMode: 'manual' | 'batch' | 'combination';
  batchRules?: BatchGenerationRule[];
  combinationMatrix?: AttributeCombinationMatrix;
  validationEnabled: boolean;
  autoGenerateSKU: boolean;
}

// 批量生成规则
export interface BatchGenerationRule {
  attributeId: string;
  valueRange: {
    start: number;
    end: number;
    step: number;
  } | {
    values: (string | number)[];
  };
}

// 属性组合矩阵
export interface AttributeCombinationMatrix {
  baseAttributes: Record<string, (string | number)[]>;
  variantAttributes: Record<string, (string | number)[]>;
  excludedCombinations?: AttributeCombination[];
}

// 属性组合
export interface AttributeCombination {
  attributes: Record<string, string | number>;
  reason: string;
}