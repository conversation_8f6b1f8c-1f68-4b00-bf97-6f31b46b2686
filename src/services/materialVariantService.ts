/**
 * 物料变体管理服务
 * 处理物料分类、模板、变体的数据操作
 */

import type {
  MaterialCategory,
  MaterialCategoryVariantConfig,
  MaterialTemplate,
  MaterialVariant,
  MaterialTemplateStockSummary,
  VariantConstraintRule,
  MaterialCodingRule
} from '@/types/material-variant'

export class MaterialVariantService {
  private baseUrl = '/mock'

  /**
   * 加载物料分类数据
   */
  async loadMaterialCategories(): Promise<MaterialCategory[]> {
    try {
      const response = await fetch(`${this.baseUrl}/metadata/materialCategory.json`)
      const data = await response.json()
      return data.data || []
    } catch (error) {
      console.error('加载物料分类失败:', error)
      throw new Error('加载物料分类失败')
    }
  }

  /**
   * 加载分类变体配置
   */
  async loadCategoryVariantConfig(categoryId: string): Promise<MaterialCategoryVariantConfig | null> {
    try {
      const response = await fetch(`${this.baseUrl}/metadata/materialCategoryVariantConfig.json`)
      const data = await response.json()
      const config = data.data?.find((c: MaterialCategoryVariantConfig) => c.categoryId === categoryId)
      return config || null
    } catch (error) {
      console.error('加载分类配置失败:', error)
      return null
    }
  }

  /**
   * 加载物料模板数据
   */
  async loadMaterialTemplates(categoryId?: string): Promise<MaterialTemplate[]> {
    try {
      const response = await fetch(`${this.baseUrl}/metadata/materialTemplate.json`)
      const data = await response.json()
      let templates = data.data || []

      // 如果指定了分类ID，过滤对应的模板
      if (categoryId) {
        templates = templates.filter((template: MaterialTemplate) =>
          template.category.id === categoryId
        )
      }

      return templates
    } catch (error) {
      console.error('加载物料模板失败:', error)
      throw new Error('加载物料模板失败')
    }
  }

  /**
   * 加载物料变体数据
   */
  async loadMaterialVariants(templateId: string): Promise<MaterialVariant[]> {
    try {
      // TODO: 实现真实的API调用
      return []
    } catch (error) {
      console.error('加载物料变体失败:', error)
      throw new Error('加载物料变体失败')
    }
  }

  /**
   * 保存物料分类
   */
  async saveMaterialCategory(category: MaterialCategory): Promise<MaterialCategory> {
    try {
      // TODO: 实现真实的API调用
      console.log('保存物料分类:', category)
      return category
    } catch (error) {
      console.error('保存物料分类失败:', error)
      throw new Error('保存物料分类失败')
    }
  }

  /**
   * 删除物料分类
   */
  async deleteMaterialCategory(categoryId: string): Promise<void> {
    try {
      // TODO: 实现真实的API调用
      console.log('删除物料分类:', categoryId)
    } catch (error) {
      console.error('删除物料分类失败:', error)
      throw new Error('删除物料分类失败')
    }
  }

  /**
   * 保存物料模板
   */
  async saveMaterialTemplate(template: MaterialTemplate): Promise<MaterialTemplate> {
    try {
      // TODO: 实现真实的API调用
      console.log('保存物料模板:', template)
      return template
    } catch (error) {
      console.error('保存物料模板失败:', error)
      throw new Error('保存物料模板失败')
    }
  }

  /**
   * 删除物料模板
   */
  async deleteMaterialTemplate(templateId: string): Promise<void> {
    try {
      // TODO: 实现真实的API调用
      console.log('删除物料模板:', templateId)
    } catch (error) {
      console.error('删除物料模板失败:', error)
      throw new Error('删除物料模板失败')
    }
  }

  /**
   * 生成物料变体SKU
   */
  generateVariantSKU(
    template: MaterialTemplate,
    baseAttributeValues: Record<string, any>,
    variantAttributeValues: Record<string, any>
  ): string {
    try {
      const codingRule = template.codingRule
      if (!codingRule) {
        return `${template.code}_${Date.now()}`
      }

      let sku = ''
      codingRule.segments.forEach((segment, index) => {
        if (index > 0) {
          sku += codingRule.separator
        }

        switch (segment.type) {
          case 'fixed':
            sku += segment.value || ''
            break
          case 'attribute':
            const attrValue = baseAttributeValues[segment.attributeId!] || variantAttributeValues[segment.attributeId!]
            if (segment.mapping && attrValue) {
              sku += segment.mapping[attrValue] || attrValue
            } else {
              sku += attrValue || ''
            }
            break
          case 'sequence':
            // 简单的序号生成，实际应该从数据库获取
            const sequence = Math.floor(Math.random() * 1000).toString().padStart(segment.length || 3, segment.padChar || '0')
            sku += sequence
            break
          case 'calculated':
            // 处理计算类型的段，如尺寸组合
            if (segment.format?.includes('{width}') && segment.format?.includes('{height}')) {
              const width = variantAttributeValues['width'] || ''
              const height = variantAttributeValues['height'] || ''
              sku += segment.format.replace('{width}', width).replace('{height}', height)
            }
            break
        }
      })

      return sku
    } catch (error) {
      console.error('生成SKU失败:', error)
      return `${template.code}_${Date.now()}`
    }
  }

  /**
   * 验证变体约束规则
   */
  validateVariantConstraints(
    variant: Partial<MaterialVariant>,
    constraintRules: VariantConstraintRule[]
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    constraintRules.forEach(rule => {
      if (!rule.isActive) return

      try {
        // 简单的表达式验证实现
        // 实际项目中应该使用更完善的表达式解析器
        const condition = rule.condition.expression
        let isConditionMet = false

        // 处理简单的条件表达式
        if (condition.includes('width > 2500') || condition.includes('height > 2500')) {
          const width = variant.variantAttributeValues?.find(v => v.attributeName === 'width')?.value as number || 0
          const height = variant.variantAttributeValues?.find(v => v.attributeName === 'height')?.value as number || 0
          isConditionMet = width > 2500 || height > 2500
        }

        if (isConditionMet && rule.action.type === 'block') {
          // 检查厚度约束
          const thickness = variant.baseAttributeValues?.find(v => v.attributeName === 'thickness')?.value as number || 0
          if (thickness < 8) {
            errors.push(rule.action.message)
          }
        }
      } catch (error) {
        console.error('验证约束规则失败:', error)
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 计算字段值
   */
  calculateFieldValues(
    variant: Partial<MaterialVariant>,
    calculatedFields: any[]
  ): Record<string, number> {
    const results: Record<string, number> = {}

    calculatedFields.forEach(field => {
      try {
        // 简单的计算实现
        if (field.name === 'area' && field.formula === 'width * height / 1000000') {
          const width = variant.variantAttributeValues?.find(v => v.attributeName === 'width')?.value as number || 0
          const height = variant.variantAttributeValues?.find(v => v.attributeName === 'height')?.value as number || 0
          results[field.name] = (width * height) / 1000000
        } else if (field.name === 'weight' && field.formula === 'area * thickness * 2.5') {
          const area = results['area'] || 0
          const thickness = variant.baseAttributeValues?.find(v => v.attributeName === 'thickness')?.value as number || 0
          results[field.name] = area * thickness * 2.5
        }
      } catch (error) {
        console.error('计算字段值失败:', error)
        results[field.name] = 0
      }
    })

    return results
  }

  /**
   * 加载库存汇总数据
   */
  async loadTemplateStockSummary(templateId: string): Promise<MaterialTemplateStockSummary | null> {
    try {
      // TODO: 实现真实的API调用
      return null
    } catch (error) {
      console.error('加载库存汇总失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const materialVariantService = new MaterialVariantService()
