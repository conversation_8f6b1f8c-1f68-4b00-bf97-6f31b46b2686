{"data": [{"id": "config_001", "categoryId": "cat_002", "enableVariants": true, "inheritFromParent": true, "overrideParentConfig": false, "inventoryManagementLevel": "variant", "baseAttributes": [{"id": "attr_thickness", "name": "thickness", "displayName": "厚度", "dataType": "select", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": true, "canOverride": false, "isSearchable": true, "isDisplayInList": true, "sortOrder": 1, "constraints": [{"id": "const_thickness_enum", "type": "enum", "enumValues": ["4", "5", "6", "8", "10", "12", "15", "19"], "errorMessage": "玻璃厚度必须选择标准规格", "isBlocking": true}], "defaultValue": "6"}, {"id": "attr_color", "name": "color", "displayName": "颜色", "dataType": "select", "isRequired": true, "isInherited": true, "canOverride": false, "isSearchable": true, "isDisplayInList": true, "sortOrder": 2, "constraints": [{"id": "const_color_enum", "type": "enum", "enumValues": ["透明", "茶色", "蓝色", "绿色", "灰色"], "errorMessage": "请选择有效的玻璃颜色", "isBlocking": true}], "defaultValue": "透明"}, {"id": "attr_grade", "name": "grade", "displayName": "等级", "dataType": "select", "isRequired": true, "isInherited": true, "canOverride": false, "isSearchable": true, "isDisplayInList": true, "sortOrder": 3, "constraints": [{"id": "const_grade_enum", "type": "enum", "enumValues": ["优等品", "一等品", "合格品"], "errorMessage": "请选择有效的玻璃等级", "isBlocking": true}], "defaultValue": "优等品"}], "variantAttributes": [{"id": "attr_width", "name": "width", "displayName": "宽度", "dataType": "integer", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": false, "canOverride": true, "isSearchable": true, "isDisplayInList": true, "sortOrder": 4, "constraints": [{"id": "const_width_range", "type": "range", "minValue": 100, "maxValue": 3660, "errorMessage": "玻璃宽度必须在100-3660mm之间", "isBlocking": true}]}, {"id": "attr_height", "name": "height", "displayName": "高度", "dataType": "integer", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": false, "canOverride": true, "isSearchable": true, "isDisplayInList": true, "sortOrder": 5, "constraints": [{"id": "const_height_range", "type": "range", "minValue": 100, "maxValue": 2440, "errorMessage": "玻璃高度必须在100-2440mm之间", "isBlocking": true}]}], "calculatedFields": [{"id": "calc_area", "name": "area", "displayName": "面积", "formula": "width * height / 1000000", "dependencies": ["width", "height"], "unit": {"id": "unit_m2", "name": "平方米", "symbol": "m²", "category": "area"}, "precision": 4, "isDisplayInList": true, "updateTrigger": "onChange", "isActive": true}, {"id": "calc_weight", "name": "weight", "displayName": "重量", "formula": "area * thickness * 2.5", "dependencies": ["area", "thickness"], "unit": {"id": "unit_kg", "name": "千克", "symbol": "kg", "category": "weight"}, "precision": 2, "isDisplayInList": true, "updateTrigger": "onChange", "isActive": true}], "codingRules": [{"id": "coding_glass_float", "categoryId": "cat_002", "name": "浮法玻璃编码规则", "template": "GLASS_FLOAT_{thickness}MM_{color}_{width}x{height}_{sequence}", "segments": [{"id": "seg_prefix", "name": "前缀", "type": "fixed", "value": "GLASS_FLOAT"}, {"id": "seg_thickness", "name": "厚度", "type": "attribute", "attributeId": "attr_thickness", "format": "integer"}, {"id": "seg_thickness_unit", "name": "厚度单位", "type": "fixed", "value": "MM"}, {"id": "seg_color", "name": "颜色", "type": "attribute", "attributeId": "attr_color", "format": "code_mapping", "mapping": {"透明": "CLR", "茶色": "BRZ", "蓝色": "BLU", "绿色": "GRN", "灰色": "GRY"}}, {"id": "seg_dimensions", "name": "尺寸", "type": "calculated", "format": "{width}x{height}"}, {"id": "seg_sequence", "name": "序号", "type": "sequence", "length": 3, "padChar": "0"}], "separator": "_", "autoGenerate": true, "isActive": true, "priority": 1}], "constraintRules": [{"id": "rule_large_panel_thickness", "name": "大板玻璃厚度限制", "categoryId": "cat_002", "ruleType": "validation", "condition": {"type": "complex", "expression": "width > 2500 OR height > 2500", "attributes": ["width", "height"]}, "action": {"type": "block", "message": "大板玻璃(宽度>2.5m或高度>2.5m)厚度不能小于8mm"}, "priority": 1, "isActive": true, "errorLevel": "error"}, {"id": "rule_area_calculation", "name": "面积自动计算", "categoryId": "cat_002", "ruleType": "calculation", "condition": {"type": "simple", "expression": "width > 0 AND height > 0", "attributes": ["width", "height"]}, "action": {"type": "calculate", "message": "自动计算玻璃面积"}, "priority": 2, "isActive": true, "errorLevel": "warning"}], "createdAt": "2024-08-07T10:00:00Z", "updatedAt": "2024-08-07T10:00:00Z"}], "success": true, "message": "物料分类变体配置数据加载成功"}