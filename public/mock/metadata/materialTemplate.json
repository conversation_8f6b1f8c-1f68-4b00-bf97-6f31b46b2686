{"data": [{"id": "tpl_glass_float_001", "name": "浮法玻璃", "code": "GLASS_FLOAT", "category": {"id": "cat_002", "name": "浮法玻璃", "code": "FLOAT_GLASS", "parentId": "cat_001", "description": "标准浮法玻璃原片", "isActive": true}, "materialType": "raw_glass", "baseAttributeValues": [{"attributeId": "attr_thickness", "attributeName": "thickness", "value": "6", "unit": "mm"}, {"attributeId": "attr_color", "attributeName": "color", "value": "透明"}, {"attributeId": "attr_grade", "attributeName": "grade", "value": "优等品"}], "variantAttributeConfigs": [{"id": "attr_width", "name": "width", "displayName": "宽度", "dataType": "integer", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": false, "canOverride": true, "isSearchable": true, "isDisplayInList": true, "sortOrder": 4, "constraints": [{"id": "const_width_range", "type": "range", "minValue": 100, "maxValue": 3660, "errorMessage": "玻璃宽度必须在100-3660mm之间", "isBlocking": true}]}, {"id": "attr_height", "name": "height", "displayName": "高度", "dataType": "integer", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": false, "canOverride": true, "isSearchable": true, "isDisplayInList": true, "sortOrder": 5, "constraints": [{"id": "const_height_range", "type": "range", "minValue": 100, "maxValue": 2440, "errorMessage": "玻璃高度必须在100-2440mm之间", "isBlocking": true}]}], "calculatedFields": [{"id": "calc_area", "name": "area", "displayName": "面积", "formula": "width * height / 1000000", "dependencies": ["width", "height"], "unit": {"id": "unit_m2", "name": "平方米", "symbol": "m²", "category": "area"}, "precision": 4, "isDisplayInList": true, "updateTrigger": "onChange", "isActive": true}, {"id": "calc_weight", "name": "weight", "displayName": "重量", "formula": "area * thickness * 2.5", "dependencies": ["area", "thickness"], "unit": {"id": "unit_kg", "name": "千克", "symbol": "kg", "category": "weight"}, "precision": 2, "isDisplayInList": true, "updateTrigger": "onChange", "isActive": true}], "enableVariants": true, "variants": [], "inventoryManagementLevel": "variant", "codingRule": {"id": "coding_glass_float", "categoryId": "cat_002", "name": "浮法玻璃编码规则", "template": "GLASS_FLOAT_{thickness}MM_{color}_{width}x{height}_{sequence}", "segments": [{"id": "seg_prefix", "name": "前缀", "type": "fixed", "value": "GLASS_FLOAT"}, {"id": "seg_thickness", "name": "厚度", "type": "attribute", "attributeId": "attr_thickness", "format": "integer"}, {"id": "seg_thickness_unit", "name": "厚度单位", "type": "fixed", "value": "MM"}, {"id": "seg_color", "name": "颜色", "type": "attribute", "attributeId": "attr_color", "format": "code_mapping", "mapping": {"透明": "CLR", "茶色": "BRZ", "蓝色": "BLU", "绿色": "GRN", "灰色": "GRY"}}, {"id": "seg_dimensions", "name": "尺寸", "type": "calculated", "format": "{width}x{height}"}, {"id": "seg_sequence", "name": "序号", "type": "sequence", "length": 3, "padChar": "0"}], "separator": "_", "autoGenerate": true, "isActive": true, "priority": 1}, "constraintRules": [{"id": "rule_large_panel_thickness", "name": "大板玻璃厚度限制", "categoryId": "cat_002", "ruleType": "validation", "condition": {"type": "complex", "expression": "width > 2500 OR height > 2500", "attributes": ["width", "height"]}, "action": {"type": "block", "message": "大板玻璃(宽度>2.5m或高度>2.5m)厚度不能小于8mm"}, "priority": 1, "isActive": true, "errorLevel": "error"}], "aggregatedStats": {"templateId": "tpl_glass_float_001", "totalVariants": 12, "totalStockQuantity": 450, "totalStockValue": 38475.0, "totalReservedQuantity": 85, "totalAvailableQuantity": 365, "totalArea": 2847.5, "totalWeight": 7118.75, "variantStats": [], "lastUpdated": "2024-08-07T10:30:00Z"}, "isActive": true, "createdAt": "2024-08-01T09:00:00Z", "updatedAt": "2024-08-07T10:30:00Z"}, {"id": "tpl_profile_alu_001", "name": "铝合金型材", "code": "PROFILE_ALU", "category": {"id": "cat_007", "name": "铝合金型材", "code": "ALU_PROFILE", "parentId": "cat_006", "description": "门窗用铝合金型材", "isActive": true}, "materialType": "profile", "baseAttributeValues": [{"attributeId": "attr_section", "attributeName": "section", "value": "50x30mm"}, {"attributeId": "attr_color", "attributeName": "color", "value": "银白色"}, {"attributeId": "attr_wall_thickness", "attributeName": "wallThickness", "value": "1.4", "unit": "mm"}], "variantAttributeConfigs": [{"id": "attr_length", "name": "length", "displayName": "长度", "dataType": "integer", "unit": {"id": "unit_mm", "name": "毫米", "symbol": "mm", "category": "length"}, "isRequired": true, "isInherited": false, "canOverride": true, "isSearchable": true, "isDisplayInList": true, "sortOrder": 1, "constraints": [{"id": "const_length_range", "type": "range", "minValue": 1000, "maxValue": 6000, "errorMessage": "型材长度必须在1000-6000mm之间", "isBlocking": true}]}], "calculatedFields": [{"id": "calc_weight", "name": "weight", "displayName": "重量", "formula": "length * 0.00285", "dependencies": ["length"], "unit": {"id": "unit_kg", "name": "千克", "symbol": "kg", "category": "weight"}, "precision": 2, "isDisplayInList": true, "updateTrigger": "onChange", "isActive": true}], "enableVariants": true, "variants": [], "inventoryManagementLevel": "variant", "codingRule": {"id": "coding_profile_alu", "categoryId": "cat_007", "name": "铝型材编码规则", "template": "PROFILE_ALU_{section}_{color}_{length}_{sequence}", "segments": [{"id": "seg_prefix", "name": "前缀", "type": "fixed", "value": "PROFILE_ALU"}, {"id": "seg_section", "name": "截面", "type": "attribute", "attributeId": "attr_section", "format": "code_mapping", "mapping": {"50x30mm": "50x30", "60x40mm": "60x40", "80x50mm": "80x50"}}, {"id": "seg_color", "name": "颜色", "type": "attribute", "attributeId": "attr_color", "format": "code_mapping", "mapping": {"银白色": "SLV", "香槟色": "CHP", "古铜色": "BRZ"}}, {"id": "seg_length", "name": "长度", "type": "attribute", "attributeId": "attr_length", "format": "integer"}, {"id": "seg_sequence", "name": "序号", "type": "sequence", "length": 3, "padChar": "0"}], "separator": "_", "autoGenerate": true, "isActive": true, "priority": 1}, "constraintRules": [], "aggregatedStats": {"templateId": "tpl_profile_alu_001", "totalVariants": 8, "totalStockQuantity": 280, "totalStockValue": 7980.0, "totalReservedQuantity": 45, "totalAvailableQuantity": 235, "totalLength": 1680.0, "totalWeight": 4788.0, "variantStats": [], "lastUpdated": "2024-08-07T10:30:00Z"}, "isActive": true, "createdAt": "2024-08-01T09:30:00Z", "updatedAt": "2024-08-07T10:30:00Z"}], "success": true, "message": "物料模板数据加载成功"}